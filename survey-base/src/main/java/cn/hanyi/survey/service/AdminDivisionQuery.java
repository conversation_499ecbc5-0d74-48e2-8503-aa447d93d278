package cn.hanyi.survey.service;

import org.geotools.data.FileDataStore;
import org.geotools.data.FileDataStoreFinder;
import org.geotools.data.simple.SimpleFeatureCollection;
import org.geotools.data.simple.SimpleFeatureSource;
import org.geotools.factory.CommonFactoryFinder;
import org.geotools.geometry.jts.JTSFactoryFinder;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.opengis.feature.simple.SimpleFeature;
import org.opengis.filter.Filter;
import org.opengis.filter.FilterFactory2;

import java.io.File;

/**
 * 使用GeoTools和ChinaAdminDivisonSHP数据通过经纬度查询省市区
 */
public class AdminDivisionQuery {

    private SimpleFeatureSource featureSource;
    private GeometryFactory geometryFactory;
    private FilterFactory2 filterFactory;

    public AdminDivisionQuery(String shapefilePath) throws Exception {
        // 1. 初始化几何和过滤器工厂
        geometryFactory = JTSFactoryFinder.getGeometryFactory();
        filterFactory = CommonFactoryFinder.getFilterFactory2();

        // 2. 加载Shapefile数据源
        File file = new File(shapefilePath);
        if (!file.exists()) {
            throw new RuntimeException("Shapefile not found: " + shapefilePath);
        }
        FileDataStore store = FileDataStoreFinder.getDataStore(file);
        // 注意：设置正确的字符集，否则中文属性可能乱码:cite[6]
        // store.setCharset(Charset.forName("GBK")); // 或 "UTF-8"，根据实际dbf文件编码调整
        this.featureSource = store.getFeatureSource();
    }

    /**
     * 根据经纬度查询行政区划信息
     *
     * @param lng 经度
     * @param lat 纬度
     * @return 包含行政区划信息的SimpleFeature，如果未找到则返回null
     */
    public SimpleFeature queryByCoordinate(double lng, double lat) throws Exception {
        // 3. 创建查询点
        Point point = geometryFactory.createPoint(new Coordinate(lng, lat));

        // 4. 创建空间过滤器：查询包含该点的要素
        Filter filter = filterFactory.contains(filterFactory.property(featureSource.getSchema().getGeometryDescriptor().getLocalName()),
                filterFactory.literal(point));

        // 5. 执行查询
        SimpleFeatureCollection features = featureSource.getFeatures(filter);

        // 6. 获取结果（假设点只落在一个行政区划内）
        SimpleFeature feature = null;
        try (var iterator = features.features()) {
            if (iterator.hasNext()) {
                feature = iterator.next();
            }
        }
        return feature;
    }

    /**
     * 打印查询结果的特征属性
     *
     * @param feature 查询返回的特征
     */
    public void printFeatureInfo(SimpleFeature feature) {
        if (feature == null) {
            System.out.println("未找到对应的行政区划。");
            return;
        }
        // 遍历所有属性并打印
        feature.getProperties().forEach(property -> {
            // 避免打印几何对象（WKT很长）
            property.getUserData();
            if (!(property.getValue() instanceof Geometry)) {
                System.out.println(property.getName() + ": " + property.getValue());
            }
        });
    }

    public static void main(String[] args) {
        try {
            // 参数：Shapefile文件路径
            // 请替换为你的实际文件路径，例如 "src/main/resources/shapefile/districts.shp"
            String shapefilePath = "/Users/<USER>/Downloads/city.shp";

            AdminDivisionQuery queryTool = new AdminDivisionQuery(shapefilePath);

            // 示例坐标（北京故宫近似坐标）
            double lng = 116.3974;
            double lat = 39.9093;

            SimpleFeature result = queryTool.queryByCoordinate(lng, lat);
            queryTool.printFeatureInfo(result);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}